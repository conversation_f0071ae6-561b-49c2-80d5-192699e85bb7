import axios from 'axios';

// 扣子 API 配置
const COZE_API_BASE_URL = 'http://192.168.1.12:8888/v3';

export interface ChatMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
  content_type?: 'text';
}

export interface ChatRequest {
  bot_id: string;
  user_id: string;
  stream: boolean;
  auto_save_history: boolean;
  additional_messages: ChatMessage[];
}

export interface ChatResponse {
  code: number;
  msg: string;
  data: {
    id: string;
    conversation_id: string;
    bot_id: string;
    created_at: number;
    completed_at: number;
    failed_at?: number;
    meta_data: any;
    last_error?: any;
    status: string;
    required_action?: any;
    usage?: {
      token_count: number;
      output_count: number;
      input_count: number;
    };
  };
}

export interface StreamChatResponse {
  event: string;
  data: string;
}

export class CozeChatService {
  private apiKey: string;
  private botId: string;

  constructor(apiKey: string, botId: string) {
    this.apiKey = apiKey;
    this.botId = botId;
  }

  // 非流式聊天
  async chat(messages: ChatMessage[], userId: string = 'default-user'): Promise<ChatResponse> {
    try {
      const response = await axios.post(
        `${COZE_API_BASE_URL}/chat`,
        {
          bot_id: this.botId,
          user_id: userId,
          stream: false,
          auto_save_history: true,
          additional_messages: messages,
        } as ChatRequest,
        {
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json',
          },
        }
      );

      return response.data;
    } catch (error) {
      console.error('扣子 API 调用失败:', error);
      throw error;
    }
  }

  // 流式聊天
  async streamChat(
    messages: ChatMessage[],
    userId: string = 'default-user',
    onMessage: (content: string) => void,
    onComplete: () => void,
    onError: (error: any) => void
  ): Promise<void> {
    try {
      const response = await fetch(`${COZE_API_BASE_URL}/chat`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          bot_id: this.botId,
          user_id: userId,
          stream: true,
          auto_save_history: true,
          additional_messages: messages,
        } as ChatRequest),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('无法获取响应流');
      }

      const decoder = new TextDecoder();
      let buffer = '';

      while (true) {
        const { done, value } = await reader.read();
        
        if (done) {
          onComplete();
          break;
        }

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.trim() === '') continue;
          
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            if (data === '[DONE]') {
              onComplete();
              return;
            }
            
            try {
              const parsed = JSON.parse(data);
              if (parsed.event === 'conversation.message.delta' && parsed.data) {
                const deltaData = JSON.parse(parsed.data);
                if (deltaData.content) {
                  onMessage(deltaData.content);
                }
              }
            } catch (e) {
              console.warn('解析流数据失败:', e);
            }
          }
        }
      }
    } catch (error) {
      console.error('流式聊天失败:', error);
      onError(error);
    }
  }

  // 获取聊天历史
  async getChatHistory(conversationId: string): Promise<any> {
    try {
      const response = await axios.get(
        `${COZE_API_BASE_URL}/chat/message/list`,
        {
          params: {
            conversation_id: conversationId,
          },
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
          },
        }
      );

      return response.data;
    } catch (error) {
      console.error('获取聊天历史失败:', error);
      throw error;
    }
  }
}
