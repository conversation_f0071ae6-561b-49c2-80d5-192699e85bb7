/* 导入 Ant Design 样式 */
@import 'antd/dist/reset.css';

:root {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB',
    'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif, 'Apple Color Emoji',
    'Segoe UI Emoji', 'Segoe UI Symbol';
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light;
  color: rgba(0, 0, 0, 0.88);
  background-color: #ffffff;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  min-width: 320px;
  min-height: 100vh;
  background-color: #f5f5f5;
}

#root {
  height: 100vh;
  overflow: hidden;
}

/* 移除默认的 focus outline，使用 Ant Design 的样式 */
button:focus,
button:focus-visible {
  outline: none;
}

/* 确保聊天界面占满全屏 */
.ant-layout {
  min-height: 100vh;
}

/* 优化移动端体验 */
@media (max-width: 768px) {
  body {
    font-size: 14px;
  }
}
