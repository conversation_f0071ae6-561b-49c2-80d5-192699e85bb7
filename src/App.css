.App {
  height: 100vh;
  overflow: hidden;
}

/* 自定义滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 聊天气泡动画 */
.chat-bubble-enter {
  opacity: 0;
  transform: translateY(20px);
}

.chat-bubble-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 300ms, transform 300ms;
}

/* 打字效果 */
.typing-indicator {
  display: inline-block;
  animation: typing 1.5s infinite;
}

@keyframes typing {
  0%, 60%, 100% {
    opacity: 1;
  }
  30% {
    opacity: 0.5;
  }
}
