import React, { useState, useRef, useEffect } from 'react';
import { 
  Conversations, 
  Sender, 
  Bubble, 
  useXAgent, 
  useXChat 
} from '@ant-design/x';
import { 
  Card, 
  Typography, 
  Space, 
  Alert, 
  Spin, 
  Button, 
  Modal, 
  Input, 
  Form,
  message
} from 'antd';
import { 
  SettingOutlined, 
  RobotOutlined, 
  UserOutlined,
  SendOutlined 
} from '@ant-design/icons';
import { CozeChatService, type ChatMessage } from '../services/cozeChatService';
import { COZE_CONFIG, validateCozeConfig } from '../config/cozeConfig';

const { Title, Text } = Typography;

interface Message {
  id: string;
  content: string;
  role: 'user' | 'assistant';
  timestamp: number;
  status?: 'sending' | 'sent' | 'error';
}

const ChatInterface: React.FC = () => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [configModalVisible, setConfigModalVisible] = useState(false);
  const [apiKey, setApiKey] = useState(COZE_CONFIG.API_KEY);
  const [botId, setBotId] = useState(COZE_CONFIG.BOT_ID);
  const [chatService, setChatService] = useState<CozeChatService | null>(null);
  const [form] = Form.useForm();
  
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // 初始化聊天服务
  useEffect(() => {
    if (validateCozeConfig()) {
      const service = new CozeChatService(COZE_CONFIG.API_KEY, COZE_CONFIG.BOT_ID);
      setChatService(service);
    }
  }, []);

  // 自动滚动到底部
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // 发送消息
  const handleSendMessage = async (content: string) => {
    if (!content.trim() || !chatService) {
      if (!chatService) {
        message.error('请先配置扣子 API');
        setConfigModalVisible(true);
      }
      return;
    }

    const userMessage: Message = {
      id: Date.now().toString(),
      content: content.trim(),
      role: 'user',
      timestamp: Date.now(),
      status: 'sent'
    };

    setMessages(prev => [...prev, userMessage]);
    setIsLoading(true);

    // 创建助手消息占位符
    const assistantMessageId = (Date.now() + 1).toString();
    const assistantMessage: Message = {
      id: assistantMessageId,
      content: '',
      role: 'assistant',
      timestamp: Date.now(),
      status: 'sending'
    };

    setMessages(prev => [...prev, assistantMessage]);

    try {
      // 准备发送给扣子的消息历史
      const chatMessages: ChatMessage[] = [
        ...messages.map(msg => ({
          role: msg.role,
          content: msg.content,
          content_type: 'text' as const
        })),
        {
          role: 'user',
          content: content.trim(),
          content_type: 'text' as const
        }
      ];

      // 使用流式聊天
      await chatService.streamChat(
        chatMessages,
        COZE_CONFIG.DEFAULT_USER_ID,
        // onMessage: 接收流式内容
        (deltaContent: string) => {
          setMessages(prev => 
            prev.map(msg => 
              msg.id === assistantMessageId 
                ? { ...msg, content: msg.content + deltaContent }
                : msg
            )
          );
        },
        // onComplete: 完成回调
        () => {
          setMessages(prev => 
            prev.map(msg => 
              msg.id === assistantMessageId 
                ? { ...msg, status: 'sent' }
                : msg
            )
          );
          setIsLoading(false);
        },
        // onError: 错误回调
        (error) => {
          console.error('聊天错误:', error);
          setMessages(prev => 
            prev.map(msg => 
              msg.id === assistantMessageId 
                ? { ...msg, content: '抱歉，发生了错误，请稍后重试。', status: 'error' }
                : msg
            )
          );
          setIsLoading(false);
          message.error('发送消息失败，请检查网络连接或API配置');
        }
      );
    } catch (error) {
      console.error('发送消息失败:', error);
      setMessages(prev => 
        prev.filter(msg => msg.id !== assistantMessageId)
      );
      setIsLoading(false);
      message.error('发送消息失败，请检查网络连接或API配置');
    }
  };

  // 保存配置
  const handleSaveConfig = () => {
    form.validateFields().then(values => {
      const newApiKey = values.apiKey.trim();
      const newBotId = values.botId.trim();
      
      if (!newApiKey || !newBotId) {
        message.error('请填写完整的配置信息');
        return;
      }

      setApiKey(newApiKey);
      setBotId(newBotId);
      
      const service = new CozeChatService(newApiKey, newBotId);
      setChatService(service);
      
      setConfigModalVisible(false);
      message.success('配置保存成功');
    });
  };

  // 清空对话
  const handleClearChat = () => {
    setMessages([]);
    message.success('对话已清空');
  };

  return (
    <div style={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      {/* 头部 */}
      <Card 
        style={{ 
          borderRadius: 0, 
          borderBottom: '1px solid #f0f0f0',
          flexShrink: 0
        }}
      >
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Space>
            <RobotOutlined style={{ fontSize: '24px', color: '#1890ff' }} />
            <Title level={4} style={{ margin: 0 }}>
              健康问答助手
            </Title>
            <Text type="secondary">基于扣子 AI 的智能对话</Text>
          </Space>
          <Space>
            <Button onClick={handleClearChat}>
              清空对话
            </Button>
            <Button 
              icon={<SettingOutlined />} 
              onClick={() => setConfigModalVisible(true)}
            >
              配置
            </Button>
          </Space>
        </div>
      </Card>

      {/* 配置提示 */}
      {!chatService && (
        <Alert
          message="需要配置扣子 API"
          description="请点击右上角的配置按钮，填入你的扣子 API Key 和 Bot ID"
          type="warning"
          showIcon
          style={{ margin: '16px' }}
          action={
            <Button size="small" onClick={() => setConfigModalVisible(true)}>
              立即配置
            </Button>
          }
        />
      )}

      {/* 聊天区域 */}
      <div style={{ flex: 1, overflow: 'hidden', padding: '0 16px' }}>
        <Conversations
          style={{ height: '100%' }}
          items={messages.map(msg => ({
            key: msg.id,
            label: msg.role === 'user' ? '用户' : '助手',
            icon: msg.role === 'user' ? <UserOutlined /> : <RobotOutlined />,
            children: (
              <Bubble
                content={msg.content || (msg.status === 'sending' ? '正在思考...' : '')}
                avatar={msg.role === 'user' ? <UserOutlined /> : <RobotOutlined />}
                placement={msg.role === 'user' ? 'end' : 'start'}
                typing={msg.status === 'sending'}
                style={{
                  maxWidth: '70%',
                  backgroundColor: msg.role === 'user' ? '#1890ff' : '#f6f6f6',
                  color: msg.role === 'user' ? 'white' : 'black',
                }}
              />
            )
          }))}
        />
        <div ref={messagesEndRef} />
      </div>

      {/* 输入区域 */}
      <div style={{ padding: '16px', borderTop: '1px solid #f0f0f0', flexShrink: 0 }}>
        <Sender
          placeholder="请输入你的健康问题..."
          onSubmit={handleSendMessage}
          loading={isLoading}
          disabled={!chatService}
          style={{ width: '100%' }}
        />
      </div>

      {/* 配置弹窗 */}
      <Modal
        title="扣子 API 配置"
        open={configModalVisible}
        onOk={handleSaveConfig}
        onCancel={() => setConfigModalVisible(false)}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            apiKey: apiKey,
            botId: botId
          }}
        >
          <Form.Item
            label="API Key"
            name="apiKey"
            rules={[{ required: true, message: '请输入扣子 API Key' }]}
          >
            <Input.Password 
              placeholder="请输入你的扣子 API Key" 
              style={{ width: '100%' }}
            />
          </Form.Item>
          
          <Form.Item
            label="Bot ID"
            name="botId"
            rules={[{ required: true, message: '请输入扣子 Bot ID' }]}
          >
            <Input 
              placeholder="请输入你的扣子 Bot ID" 
              style={{ width: '100%' }}
            />
          </Form.Item>
          
          <Alert
            message="配置说明"
            description={
              <div>
                <p>1. 登录扣子平台 (coze.cn) 获取 API Key</p>
                <p>2. 创建或选择一个 Bot，获取 Bot ID</p>
                <p>3. 确保 Bot 已发布并可以正常使用</p>
              </div>
            }
            type="info"
            showIcon
          />
        </Form>
      </Modal>
    </div>
  );
};

export default ChatInterface;
